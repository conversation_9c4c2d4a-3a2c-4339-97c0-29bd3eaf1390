import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { z } from "zod";
import { NocoBaseClient, type DesktopRoute } from "../client.js";
import { createFormattedResponse, createFormattedErrorResponse } from "../utils/response-formatter.js";

// 页面模板定义
export const PAGE_TEMPLATES = {
  blank: {
    name: "空白页面",
    description: "创建一个空白页面",
    schema: {
      type: "void",
      "x-component": "Page",
      "x-async": false
    }
  },
  table: {
    name: "表格页面",
    description: "创建一个包含数据表格的页面",
    schema: (collectionName?: string) => ({
      type: "void",
      "x-component": "Page",
      properties: {
        grid: {
          type: "void",
          "x-component": "Grid",
          properties: {
            table: {
              type: "void",
              "x-component": "TableBlockProvider",
              "x-component-props": {
                collection: collectionName || "users",
                action: "list"
              },
              properties: {
                table: {
                  type: "array",
                  "x-component": "TableV2",
                  "x-component-props": {
                    rowKey: "id",
                    rowSelection: {
                      type: "checkbox"
                    }
                  }
                }
              }
            }
          }
        }
      }
    })
  },
  dashboard: {
    name: "仪表板页面",
    description: "创建一个仪表板页面",
    schema: {
      type: "void",
      "x-component": "Page",
      properties: {
        grid: {
          type: "void",
          "x-component": "Grid",
          "x-initializer": "page:addBlock",
          properties: {
            stats: {
              type: "void",
              "x-component": "CardItem",
              "x-component-props": {
                title: "统计信息"
              }
            }
          }
        }
      }
    }
  }
};

// 生成唯一 UID
function generateUID(prefix: string): string {
  return `${prefix}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
}

// 注册路由工具
export async function registerRouteTools(server: McpServer, client: NocoBaseClient): Promise<void> {
  // List routes tool
  server.registerTool(
    "list_routes",
    {
      title: "List Routes",
      description: "List all routes in NocoBase with optional tree structure",
      inputSchema: {
        tree: z.boolean().optional().describe("Return routes in tree structure").default(true)
      }
    },
    async ({ tree = true }) => {
      try {
        const routes = await client.listRoutes({ tree });
        return createFormattedResponse(
          `Found ${routes.length} routes:`,
          routes,
          'routes'
        );
      } catch (error) {
        return createFormattedErrorResponse(error);
      }
    }
  );
  // Get route tool
  server.registerTool(
    "get_route",
    {
      title: "Get Route",
      description: "Get detailed information about a specific route",
      inputSchema: {
        id: z.union([z.string(), z.number()]).describe("Route ID to retrieve")
      }
    },
    async ({ id }) => {
      try {
        const route = await client.getRoute(id);
        return {
          content: [{
            type: "text",
            text: `Route details:\n\n${JSON.stringify(route, null, 2)}`
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error getting route: ${error instanceof Error ? error.message : String(error)}`
          }],
          isError: true
        };
      }
    }
  );
  // 旧的 create_page_route 工具已删除，使用新的 create_page_route 替代
  // Create group route tool
  server.registerTool(
    "create_group_route",
    {
      title: "Create Group Route",
      description: "Create a new group route (menu group) in NocoBase",
      inputSchema: {
        title: z.string().describe("Group title"),
        parentId: z.union([z.string(), z.number()]).optional().describe("Parent route ID (for nested groups)"),
        icon: z.string().optional().describe("Ant Design icon name (e.g., 'AppstoreOutlined', 'SettingOutlined')"),
        children: z.array(z.object({
          type: z.enum(["page", "link", "group"]),
          title: z.string(),
          icon: z.string().optional(),
          href: z.string().optional(),
          openInNewWindow: z.boolean().optional()
        })).optional().describe("Child routes to create within this group")
      }
    },
    async ({ title, parentId, icon, children }) => {
      try {
        const groupRoute: Partial<DesktopRoute> = {
          type: "group",
          title,
          ...(parentId && { parentId }),
          ...(icon && { icon }),
          ...(children && { children: children as DesktopRoute[] })
        };

        const result = await client.createRoute(groupRoute);
        return {
          content: [{
            type: "text",
            text: `Group route created successfully:\n\n${JSON.stringify(result, null, 2)}`
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error creating group route: ${error instanceof Error ? error.message : String(error)}`
          }],
          isError: true
        };
      }
    }
  );
  // Create link route tool
  server.registerTool(
    "create_link_route",
    {
      title: "Create Link Route",
      description: "Create a new link route in NocoBase",
      inputSchema: {
        title: z.string().describe("Link title"),
        href: z.string().describe("URL to link to"),
        parentId: z.union([z.string(), z.number()]).optional().describe("Parent route ID (for nested links)"),
        icon: z.string().optional().describe("Ant Design icon name (e.g., 'LinkOutlined', 'GlobalOutlined')"),
        openInNewWindow: z.boolean().optional().default(true).describe("Open link in new window"),
        params: z.array(z.object({
          name: z.string(),
          value: z.string()
        })).optional().describe("URL parameters")
      }
    },
    async ({ title, href, parentId, icon, openInNewWindow = true, params }) => {
      try {
        const linkRoute: Partial<DesktopRoute> = {
          type: "link",
          title,
          ...(parentId && { parentId }),
          ...(icon && { icon }),
          options: {
            href,
            openInNewWindow,
            ...(params && { params })
          }
        };

        const result = await client.createRoute(linkRoute);
        return {
          content: [{
            type: "text",
            text: `Link route created successfully:\n\n${JSON.stringify(result, null, 2)}`
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error creating link route: ${error instanceof Error ? error.message : String(error)}`
          }],
          isError: true
        };
      }
    }
  );

  // Update route tool
  server.registerTool(
    "update_route",
    {
      title: "Update Route",
      description: "Update an existing route",
      inputSchema: {
        id: z.union([z.string(), z.number()]).describe("Route ID to update"),
        title: z.string().optional().describe("New title"),
        icon: z.string().optional().describe("New icon"),
        hidden: z.boolean().optional().describe("Hide/show route"),
        href: z.string().optional().describe("New URL (for link routes)"),
        openInNewWindow: z.boolean().optional().describe("Open in new window (for link routes)")
      }
    },
    async ({ id, title, icon, hidden, href, openInNewWindow }) => {
      try {
        const updates: Partial<DesktopRoute> = {};
        if (title) updates.title = title;
        if (icon) updates.icon = icon;
        if (hidden !== undefined) updates.hidden = hidden;
        if (href || openInNewWindow !== undefined) {
          updates.options = {
            ...(href && { href }),
            ...(openInNewWindow !== undefined && { openInNewWindow })
          };
        }

        const result = await client.updateRoute(id, updates);
        return {
          content: [{
            type: "text",
            text: `Route updated successfully:\n\n${JSON.stringify(result, null, 2)}`
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error updating route: ${error instanceof Error ? error.message : String(error)}`
          }],
          isError: true
        };
      }
    }
  );
  // Delete route tool
  server.registerTool(
    "delete_route",
    {
      title: "Delete Route",
      description: "Delete a route and all its children",
      inputSchema: {
        id: z.union([z.string(), z.number()]).describe("Route ID to delete")
      }
    },
    async ({ id }) => {
      try {
        await client.deleteRoute(id);
        return {
          content: [{
            type: "text",
            text: `Route ${id} deleted successfully`
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error deleting route: ${error instanceof Error ? error.message : String(error)}`
          }],
          isError: true
        };
      }
    }
  );

  // Move route tool
  server.registerTool(
    "move_route",
    {
      title: "Move Route",
      description: "Move a route to a different position or parent",
      inputSchema: {
        sourceId: z.union([z.string(), z.number()]).describe("Route ID to move"),
        targetId: z.union([z.string(), z.number()]).optional().describe("Target route ID to move after"),
        method: z.enum(["insertAfter", "prepend"]).optional().default("insertAfter").describe("Move method")
      }
    },
    async ({ sourceId, targetId, method = "insertAfter" }) => {
      try {
        await client.moveRoute({
          sourceId,
          ...(targetId && { targetId }),
          method
        });
        return {
          content: [{
            type: "text",
            text: `Route ${sourceId} moved successfully`
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error moving route: ${error instanceof Error ? error.message : String(error)}`
          }],
          isError: true
        };
      }
    }
  );

  // 创建页面路由工具
  server.registerTool(
    "create_page_route",
    {
      title: "Create Page Route",
      description: "Create a new page route in NocoBase with proper tab support and menu schema",
      inputSchema: {
        title: z.string().describe("Page title"),
        parentId: z.union([z.string(), z.number()]).optional().describe("Parent route ID (for nested routes)"),
        icon: z.string().optional().describe("Ant Design icon name (e.g., 'UserOutlined', 'SettingOutlined')"),
        template: z.enum(["blank", "table", "dashboard"]).optional().default("blank").describe("Page template type"),
        collectionName: z.string().optional().describe("Collection name for table template"),
        enableTabs: z.boolean().optional().default(true).describe("Enable tabs for this page"),
        hidden: z.boolean().optional().default(false).describe("Hide this route from menu")
      }
    },
    async ({ title, parentId, icon, hidden = false }) => {
      try {
        const pageSchemaUid = generateUID("page");
        const menuSchemaUid = generateUID("menu");

        // 生成 Grid 组件的 UID 和名称
        const gridSchemaUid = generateUID("grid");
        const gridSchemaName = generateUID("grid");

        // 根据修复方案创建正确的页面 schema 结构
        const pageSchema = {
          type: "void",
          "x-component": "Page",
          "x-async": false, // 根据修复方案，Page 组件应该是 x-async: false
          properties: {
            [gridSchemaName]: {
              type: "void",
              "x-component": "Grid",
              "x-initializer": "page:addBlock", // 这是关键的"Add block"功能
              "x-uid": gridSchemaUid,
              "x-async": true,
              "x-index": 1
            }
          }
          // 注意：不在这里设置 x-uid，让 createPageSchema 方法处理
        };

        console.log('🔧 Creating page with schema:', JSON.stringify(pageSchema, null, 2));

        // 创建页面 schema（使用修复后的方法）
        const schemaResult = await client.createPageSchema(pageSchemaUid, pageSchema);

        // 获取 API 返回的实际 UID
        const actualSchemaUid = schemaResult?.['x-uid'] || pageSchemaUid;

        // 创建路由记录（使用实际的 schema UID）
        const pageRoute: Partial<DesktopRoute> = {
          type: "page",
          title,
          schemaUid: actualSchemaUid,  // 使用实际的 UID
          menuSchemaUid: menuSchemaUid,
          ...(parentId && { parentId }),
          ...(icon && { icon }),
          enableTabs: false,  // 根据修复方案，简化的页面不需要 tabs
          hidden,
          sort: parentId ? 1 : 10
        };

        const routeResult = await client.createRoute(pageRoute);

        // 创建菜单项 Schema
        const menuItemSchema = {
          type: "void",
          title,
          'x-component': "Menu.Item",
          'x-designer': "Menu.Item.Designer",
          'x-component-props': {
            ...(icon && { icon }),
            __route__: routeResult
          },
          'x-uid': menuSchemaUid,
          'x-async': false
        };

        // 确定父菜单UID
        let parentMenuUid = "nocobase-admin-menu";
        if (parentId) {
          try {
            const parentRoute = await client.getRoute(parentId);
            if (parentRoute && parentRoute.menuSchemaUid) {
              parentMenuUid = parentRoute.menuSchemaUid;
            }
          } catch (error) {
            console.warn(`无法获取父路由 ${parentId} 的信息，使用默认菜单`);
          }
        }

        // 插入菜单项
        await client.insertMenuSchema(parentMenuUid, menuItemSchema);

        return {
          content: [{
            type: "text",
            text: `✅ Page route created successfully with fixed structure!

📋 Route Details:
${JSON.stringify(routeResult, null, 2)}

🔧 Schema Details:
- Page UID: ${pageSchemaUid}
- Menu UID: ${menuSchemaUid}
- Grid UID: ${gridSchemaUid}
- Grid Schema Name: ${gridSchemaName}

🎯 Key Features:
- ✅ Proper Grid component with x-initializer: "page:addBlock"
- ✅ Two-step schema creation (base Page + Grid component)
- ✅ Correct schema structure matching working pages
- ✅ "Add block" functionality should work properly

🌐 Access URL: ${process.env.NOCOBASE_BASE_URL?.replace('/api', '') || 'http://localhost:13000'}/apps/${process.env.NOCOBASE_APP || 'main'}/admin/${actualSchemaUid}`
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error creating page route: ${error instanceof Error ? error.message : String(error)}`
          }],
          isError: true
        };
      }
    }
  );

  // 验证页面结构工具
  server.registerTool(
    "verify_page_structure",
    {
      title: "Verify Page Structure",
      description: "Verify that a page has the correct schema structure for adding blocks",
      inputSchema: {
        schemaUid: z.string().describe("Page schema UID to verify"),
        routeId: z.union([z.string(), z.number()]).optional().describe("Route ID to verify (alternative to schemaUid)")
      }
    },
    async ({ schemaUid, routeId }) => {
      try {
        let targetSchemaUid = schemaUid;

        // 如果提供了 routeId，先获取对应的 schemaUid
        if (routeId && !schemaUid) {
          const route = await client.getRoute(routeId);
          if (!route || !route.schemaUid) {
            return {
              content: [{
                type: "text",
                text: `❌ Route ${routeId} not found or has no schemaUid`
              }],
              isError: true
            };
          }
          targetSchemaUid = route.schemaUid;
        }

        // 获取页面 schema
        const rawPageSchema = await client.getPageSchema(targetSchemaUid);

        // 处理可能被包装在 values 中的 schema
        let pageSchema = rawPageSchema;
        if (rawPageSchema.values && typeof rawPageSchema.values === 'object') {
          console.log('🔧 Detected schema wrapped in values, extracting...');
          pageSchema = rawPageSchema.values;
        }

        // 验证结构
        const verification = {
          schemaUid: targetSchemaUid,
          isValid: true,
          issues: [] as string[],
          hasAddBlockFeature: false,
          structure: {
            type: pageSchema.type,
            component: pageSchema['x-component'],
            async: pageSchema['x-async'],
            hasProperties: !!pageSchema.properties,
            propertiesCount: pageSchema.properties ? Object.keys(pageSchema.properties).length : 0
          }
        };

        // 检查基本结构
        if (pageSchema.type !== 'void') {
          verification.issues.push('❌ Page type should be "void"');
          verification.isValid = false;
        }

        if (pageSchema['x-component'] !== 'Page') {
          verification.issues.push('❌ Page component should be "Page"');
          verification.isValid = false;
        }

        // 检查是否有 Grid 组件
        if (pageSchema.properties) {
          for (const [propName, propSchema] of Object.entries(pageSchema.properties)) {
            const prop = propSchema as any;
            if (prop['x-component'] === 'Grid') {
              if (prop['x-initializer'] === 'page:addBlock') {
                verification.hasAddBlockFeature = true;
              } else {
                verification.issues.push(`❌ Grid component "${propName}" missing x-initializer: "page:addBlock"`);
                verification.isValid = false;
              }
            }
          }
        } else {
          verification.issues.push('❌ Page has no properties (should have Grid component)');
          verification.isValid = false;
        }

        if (!verification.hasAddBlockFeature) {
          verification.issues.push('❌ No Grid component with "page:addBlock" initializer found');
          verification.isValid = false;
        }

        return {
          content: [{
            type: "text",
            text: `📋 Page Structure Verification for ${targetSchemaUid}

${verification.isValid ? '✅ VALID' : '❌ INVALID'} - Page structure ${verification.isValid ? 'is correct' : 'has issues'}

🔧 Structure Details:
${JSON.stringify(verification.structure, null, 2)}

${verification.hasAddBlockFeature ? '✅' : '❌'} Add Block Feature: ${verification.hasAddBlockFeature ? 'Available' : 'Missing'}

${verification.issues.length > 0 ? `\n🚨 Issues Found:\n${verification.issues.join('\n')}` : '\n🎉 No issues found!'}

📊 Full Schema:
${JSON.stringify(pageSchema, null, 2)}`
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error verifying page structure: ${error instanceof Error ? error.message : String(error)}`
          }],
          isError: true
        };
      }
    }
  );


}
