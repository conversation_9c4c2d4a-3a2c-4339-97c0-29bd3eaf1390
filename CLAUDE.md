- 使用 nocobase mcp 创建、管理 nocobase 平台。
- 使用 playwright 检查 nocobase 前端效果。

The following NocoBase development environment configuration for testing the MCP server:

**Environment Details:**
- **Admin Dashboard URL**: "http://nocobase.nocobasedocker.orb.local/apps/mcp_playground/admin"
- **Test Credentials**: 
  - Username: `<EMAIL>`
  - Password: `neo@123`
- **API Configuration**:
  - Base URL: `http://nocobase.nocobasedocker.orb.local/api`
  - App ID: `mcp_playground`
  - Authorization Token: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTUwNTI2MDEsImV4cCI6MzMzMTI2NTI2MDF9.2IzOVJB0G2VqE2zQpsULLXUhEw87dmLYAtKGcwB_wyw`

**Testing Methods:**
1. **UI Testing**: Use Playwright to automate interactions with the admin dashboard at the provided URL
2. **API Testing**: Make direct HTTP requests to the API endpoints using the provided base URL, app ID, and authorization token

**Usage Context**: This environment should be used for testing the mcp-server-nocobase integration, validating that the MCP tools can properly interact with NocoBase collections, records, and schemas through both the web interface and REST API.