#!/usr/bin/env node

/**
 * 测试修复后的页面创建功能
 * 验证页面是否具有正确的结构和"Add block"功能
 */

import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 测试配置
const testConfig = {
  baseUrl: 'http://nocobase.nocobasedocker.orb.local/api',
  app: 'mcp_playground',
  token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTUwNTI2MDEsImV4cCI6MzMzMTI2NTI2MDF9.2IzOVJB0G2VqE2zQpsULLXUhEw87dmLYAtKGcwB_wyw'
};

async function testFixedPageCreation() {
  console.log('🧪 测试修复后的页面创建功能\n');

  // 启动 MCP 服务器
  const serverPath = path.join(__dirname, '../dist/index.js');
  const server = spawn('node', [serverPath], {
    stdio: ['pipe', 'pipe', 'pipe'],
    env: {
      ...process.env,
      NOCOBASE_BASE_URL: testConfig.baseUrl,
      NOCOBASE_APP: testConfig.app,
      NOCOBASE_TOKEN: testConfig.token
    }
  });

  let responseBuffer = '';
  let requestId = 1;

  // 处理服务器输出
  server.stdout.on('data', (data) => {
    responseBuffer += data.toString();
    
    // 尝试解析完整的 JSON 响应
    const lines = responseBuffer.split('\n');
    responseBuffer = lines.pop() || ''; // 保留不完整的行
    
    for (const line of lines) {
      if (line.trim()) {
        try {
          const response = JSON.parse(line);
          handleResponse(response);
        } catch (e) {
          // 忽略解析错误
        }
      }
    }
  });

  server.stderr.on('data', (data) => {
    console.error('Server error:', data.toString());
  });

  // 发送请求的辅助函数
  function sendRequest(method, params = {}) {
    const request = {
      jsonrpc: '2.0',
      id: requestId++,
      method,
      params
    };
    server.stdin.write(JSON.stringify(request) + '\n');
    return request.id;
  }

  // 处理响应的辅助函数
  function handleResponse(response) {
    if (response.id === 1) {
      // 初始化响应
      console.log('✅ MCP 服务器初始化成功');
      
      // 创建测试页面
      console.log('\n📋 Step 1: 创建测试页面');
      sendRequest('tools/call', {
        name: 'create_page_route',
        arguments: {
          title: `修复测试页面 ${Date.now()}`,
          template: 'blank',
          icon: 'ExperimentOutlined'
        }
      });
      
    } else if (response.id === 2) {
      // 页面创建响应
      if (response.result && response.result.content) {
        console.log('✅ 页面创建成功:');
        console.log(response.result.content[0].text);
        
        // 从响应中提取 schemaUid - 使用路由返回的实际 schemaUid
        const text = response.result.content[0].text;
        const routeDetailsMatch = text.match(/"schemaUid":\s*"([^"]+)"/);

        if (routeDetailsMatch) {
          const actualSchemaUid = routeDetailsMatch[1];
          console.log(`\n📋 Step 2: 验证页面结构 (实际 schemaUid: ${actualSchemaUid})`);

          // 验证页面结构
          sendRequest('tools/call', {
            name: 'verify_page_structure',
            arguments: {
              schemaUid: actualSchemaUid
            }
          });
        } else {
          console.log('❌ 无法从响应中提取实际的 schemaUid');
          console.log('响应内容:', text);
          server.kill();
        }
      } else {
        console.log('❌ 页面创建失败:', response.error || response);
        server.kill();
      }
      
    } else if (response.id === 3) {
      // 页面结构验证响应
      if (response.result && response.result.content) {
        console.log('📊 页面结构验证结果:');
        console.log(response.result.content[0].text);
        
        // 检查验证结果
        const text = response.result.content[0].text;
        const isValid = text.includes('✅ VALID');
        const hasAddBlock = text.includes('✅ Add Block Feature: Available');
        
        console.log('\n🎯 测试结果总结:');
        console.log(`   页面结构: ${isValid ? '✅ 正确' : '❌ 有问题'}`);
        console.log(`   Add Block 功能: ${hasAddBlock ? '✅ 可用' : '❌ 不可用'}`);
        
        if (isValid && hasAddBlock) {
          console.log('\n🎉 修复成功！页面创建功能已完全正常工作！');
        } else {
          console.log('\n⚠️ 修复可能不完整，请检查上述验证结果');
        }
        
      } else {
        console.log('❌ 页面结构验证失败:', response.error || response);
      }
      
      server.kill();
    }
  }

  // 等待服务器启动
  await new Promise(resolve => setTimeout(resolve, 2000));

  // 初始化 MCP 连接
  sendRequest('initialize', {
    protocolVersion: '2024-11-05',
    capabilities: {},
    clientInfo: {
      name: 'test-client',
      version: '1.0.0'
    }
  });

  // 等待测试完成
  return new Promise((resolve) => {
    server.on('close', (code) => {
      console.log(`\n🔚 测试完成，服务器退出码: ${code}`);
      resolve(code);
    });
  });
}

// 运行测试
testFixedPageCreation().catch(console.error);
